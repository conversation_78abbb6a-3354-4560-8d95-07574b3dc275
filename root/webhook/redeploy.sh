#!/bin/bash
set -e  # Exit on any error

echo "Starting redeployment process..."
echo "Timestamp: $(date)"

# Change to project directory
cd ~/remote-mt5 || {
  echo "Error: Could not change to ~/remote-mt5 directory"
  exit 1
}

echo "Stopping existing containers..."
docker compose down

echo "Fetching latest changes from git..."
git fetch --all

echo "Resetting to latest main branch..."
git reset --hard origin/main

echo "Cleaning up config directory..."
rm -rf config

echo "Building and starting containers..."
docker compose up --build -d

echo "Redeployment completed successfully!"
echo "Timestamp: $(date)"