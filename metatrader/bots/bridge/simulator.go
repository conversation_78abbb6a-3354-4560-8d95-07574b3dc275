package bridge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"google.golang.org/grpc"
)

type ClosedTrade struct {
	Ticket     int64
	Symbol     string
	Type       int64
	Volume     float32
	OpenPrice  float32
	ClosePrice float32
	OpenTime   int64
	CloseTime  int64
	Profit     float32
}

type SimulatedClient struct {
	mu           sync.Mutex
	balance      float32
	equity       float32
	margin       float32
	freeMargin   float32
	positions    []AccountPositionType
	lastTicket   int64
	currentPrice float32
	nowMs        int64
	spread       float32
	// reporting state
	closedTrades []ClosedTrade
	openTimes    map[int64]int64
	equityPeak   float32
	maxDrawdown  float32
	// risk management
	initialBalance float32
	marginCall     float32 // margin call level (e.g., 50%)
	stopOut        float32 // stop out level (e.g., 20%)
	isBlownOut     bool    // account blown flag
	totalTrades    int64   // total trades placed
	winningTrades  int64   // winning closed trades
	losingTrades   int64   // losing closed trades
	// consecutive tracking
	currentWinStreak  int64 // current consecutive wins
	currentLossStreak int64 // current consecutive losses
	maxWinStreak      int64 // maximum consecutive wins
	maxLossStreak     int64 // maximum consecutive losses
}

func NewSimulatedClient(initialBalance float32, spread float32) *SimulatedClient {
	return &SimulatedClient{
		balance:        initialBalance,
		equity:         initialBalance,
		freeMargin:     initialBalance,
		spread:         spread,
		openTimes:      make(map[int64]int64),
		equityPeak:     initialBalance,
		maxDrawdown:    0,
		initialBalance: initialBalance,
		marginCall:     0.5, // 50% margin call level
		stopOut:        0.2, // 20% stop out level
		isBlownOut:     false,
		totalTrades:    0,
		winningTrades:  0,
		losingTrades:   0,
	}
}

// UpdatePrice updates mark-to-market, triggers SL/TP, and advances time.
func (s *SimulatedClient) UpdatePrice(mid float32, t int64) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Skip if account is blown out
	if s.isBlownOut {
		return
	}

	s.currentPrice = mid
	s.nowMs = t

	// Walk positions to check SL/TP hits
	var remaining []AccountPositionType
	for _, p := range s.positions {
		// compute unrealized PnL and SL/TP triggers at this price
		p.CurrentPrice = mid
		// SL/TP evaluation uses bid for sells and ask for buys; here we approximate with mid
		closed := false
		var realized float32 = 0
		contractSize := s.getContractSize(p.Symbol) // Symbol-specific contract size
		if p.Type == 0 {                            // buy
			if p.TakeProfit > 0 && mid >= p.TakeProfit {
				realized = (p.TakeProfit - p.OpenPrice) * p.Volume * contractSize
				fmt.Println(p.TakeProfit - p.OpenPrice)
				closed = true
			} else if p.StopLoss > 0 && mid <= p.StopLoss {
				realized = (p.StopLoss - p.OpenPrice) * p.Volume * contractSize
				fmt.Println(p.StopLoss - p.OpenPrice)
				closed = true
			}
		} else { // sell
			if p.TakeProfit > 0 && mid <= p.TakeProfit {
				realized = (p.OpenPrice - p.TakeProfit) * p.Volume * contractSize
				closed = true
			} else if p.StopLoss > 0 && mid >= p.StopLoss {
				realized = (p.OpenPrice - p.StopLoss) * p.Volume * contractSize
				closed = true
			}
		}
		if closed {
			s.balance += realized
			ct := ClosedTrade{Ticket: p.Ticket, Symbol: p.Symbol, Type: p.Type, Volume: p.Volume, OpenPrice: p.OpenPrice, ClosePrice: mid, OpenTime: s.openTimes[p.Ticket], CloseTime: t, Profit: realized}
			s.closedTrades = append(s.closedTrades, ct)
			delete(s.openTimes, p.Ticket)

			// Track win/loss statistics and consecutive streaks
			isWin := realized > 0
			if isWin {
				s.winningTrades++
			} else {
				s.losingTrades++
			}
			s.updateConsecutiveStreaks(isWin)
			continue
		}
		remaining = append(remaining, p)
	}
	s.positions = remaining

	// Recompute equity/freeMargin
	unrealized := float32(0)
	contractSize := float32(100000) // Standard lot size for forex
	for i := range s.positions {
		p := &s.positions[i]
		if p.Type == 0 { // buy
			unrealized += (s.currentPrice - p.OpenPrice) * p.Volume * contractSize
		} else {
			unrealized += (p.OpenPrice - s.currentPrice) * p.Volume * contractSize
		}
	}
	s.equity = s.balance + unrealized
	s.margin = 0
	s.freeMargin = s.equity - s.margin

	// drawdown tracking
	if s.equity > s.equityPeak {
		s.equityPeak = s.equity
	} else {
		drop := s.equityPeak - s.equity
		if drop > s.maxDrawdown {
			s.maxDrawdown = drop
		}
	}

	// Account protection - check for margin call and stop out
	equityPercent := s.equity / s.initialBalance
	if equityPercent <= s.stopOut {
		// Stop out - close all positions and mark account as blown
		s.isBlownOut = true
		s.positions = []AccountPositionType{} // Close all positions
		fmt.Printf("STOP OUT: Account equity fell to %.2f%% (%.2f), closing all positions\n", equityPercent*100, s.equity)
	} else if equityPercent <= s.marginCall {
		fmt.Printf("MARGIN CALL: Account equity at %.2f%% (%.2f)\n", equityPercent*100, s.equity)
	}

	// Prevent negative equity
	if s.equity < 0 {
		s.equity = 0
		s.balance = 0
		s.freeMargin = 0
		s.isBlownOut = true
		s.positions = []AccountPositionType{} // Close all positions
		fmt.Printf("ACCOUNT BLOWN: Equity went negative, account closed\n")
	}
}

// GetAccountHealth returns account health metrics
func (s *SimulatedClient) GetAccountHealth() map[string]interface{} {
	s.mu.Lock()
	defer s.mu.Unlock()

	equityPercent := s.equity / s.initialBalance
	return map[string]interface{}{
		"equity_percent":    equityPercent * 100,
		"is_blown_out":      s.isBlownOut,
		"margin_call_level": s.marginCall * 100,
		"stop_out_level":    s.stopOut * 100,
		"total_trades":      s.totalTrades,
		"winning_trades":    s.winningTrades,
		"losing_trades":     s.losingTrades,
		"open_positions":    len(s.positions),
		"max_drawdown":      s.maxDrawdown,
		"equity_peak":       s.equityPeak,
	}
}

// updateConsecutiveStreaks updates win/loss streak tracking
func (s *SimulatedClient) updateConsecutiveStreaks(isWin bool) {
	if isWin {
		// Reset loss streak and increment win streak
		s.currentLossStreak = 0
		s.currentWinStreak++
		if s.currentWinStreak > s.maxWinStreak {
			s.maxWinStreak = s.currentWinStreak
		}
	} else {
		// Reset win streak and increment loss streak
		s.currentWinStreak = 0
		s.currentLossStreak++
		if s.currentLossStreak > s.maxLossStreak {
			s.maxLossStreak = s.currentLossStreak
		}
	}
}

// getContractSize returns the contract size for different instrument types
func (s *SimulatedClient) getContractSize(symbol string) float32 {
	// Synthetic indices (Volatility, Boom, Crash) use contract size of 1
	if strings.Contains(symbol, "Volatility") ||
		strings.Contains(symbol, "Boom") ||
		strings.Contains(symbol, "Crash") ||
		strings.Contains(symbol, "Step") ||
		strings.Contains(symbol, "Jump") {
		return 1.0
	}

	// Crypto pairs typically use 1 as well
	if strings.Contains(symbol, "BTC") ||
		strings.Contains(symbol, "ETH") ||
		strings.Contains(symbol, "ADA") ||
		strings.Contains(symbol, "SOL") {
		return 1.0
	}

	// Gold and metals
	if strings.Contains(symbol, "XAU") ||
		strings.Contains(symbol, "Gold") {
		return 100.0
	}

	// Standard forex pairs
	return 100000.0
}

// getPipSize returns the pip size for different instrument types
func (s *SimulatedClient) getPipSize(symbol string) float32 {
	// Synthetic indices use 0.01 as pip size
	if strings.Contains(symbol, "Volatility") ||
		strings.Contains(symbol, "Boom") ||
		strings.Contains(symbol, "Crash") ||
		strings.Contains(symbol, "Step") ||
		strings.Contains(symbol, "Jump") ||
		strings.Contains(symbol, "XAU") ||
		strings.Contains(symbol, "Gold") {
		return 0.01
	}

	// JPY pairs use 0.01
	if strings.Contains(symbol, "JPY") {
		return 0.01
	}

	// Most other pairs use 0.0001
	return 0.0001
}

// IsAccountBlown returns true if account is blown out
func (s *SimulatedClient) IsAccountBlown() bool {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.isBlownOut
}

// --- BridgeRpcServiceClient interface implementation ---

func (s *SimulatedClient) TickStart(ctx context.Context, in *TickStartRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[TickType], error) {
	return nil, fmt.Errorf("TickStart not supported in simulation")
}

func (s *SimulatedClient) TickStop(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GenericResponseType, error) {
	return &GenericResponseType{Status: 1, Message: "ok"}, nil
}

func (s *SimulatedClient) GetAccount(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetAccountResponse, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	return &GetAccountResponse{
		Balance:    s.balance,
		Equity:     s.equity,
		Margin:     s.margin,
		FreeMargin: s.freeMargin,
	}, nil
}

func (s *SimulatedClient) GetPositions(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetPositionsResponse, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	// return a copy for safety
	out := make([]*AccountPositionType, 0, len(s.positions))
	for _, p := range s.positions {
		pp := p
		out = append(out, &pp)
	}
	return &GetPositionsResponse{Positions: out, TotalPositions: int64(len(out))}, nil
}

func (s *SimulatedClient) GetOrders(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetOrdersResponse, error) {
	return &GetOrdersResponse{}, nil
}

func (s *SimulatedClient) GetAvailableSymbols(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetAvailableSymbolsResponse, error) {
	return &GetAvailableSymbolsResponse{}, nil
}

func (s *SimulatedClient) GetTradeHistory(ctx context.Context, in *GetTradeHistoryRequest, opts ...grpc.CallOption) (*GetTradeHistoryResponse, error) {
	return &GetTradeHistoryResponse{}, nil
}

func (s *SimulatedClient) GetTicksFrom(ctx context.Context, in *GetTicksFromRequest, opts ...grpc.CallOption) (*GetTicksResponse, error) {
	return &GetTicksResponse{}, errors.New("not implemented in simulation")
}

func (s *SimulatedClient) GetTicksRange(ctx context.Context, in *GetTicksRangeRequest, opts ...grpc.CallOption) (*GetTicksResponse, error) {
	return &GetTicksResponse{}, errors.New("not implemented in simulation")
}

func (s *SimulatedClient) CloseTrade(ctx context.Context, in *CloseTradeRequest, opts ...grpc.CallOption) (*GenericResponseType, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	idx := -1
	for i, p := range s.positions {
		if p.Ticket == in.Ticket {
			idx = i
			break
		}
	}
	if idx == -1 {
		return &GenericResponseType{Status: 0, Message: "ticket not found"}, nil
	}
	p := &s.positions[idx]
	price := s.currentPrice
	if in.Price != nil && *in.Price != 0 {
		price = *in.Price
	}
	var realized float32
	contractSize := s.getContractSize(p.Symbol) // Symbol-specific contract size
	if p.Type == 0 {
		realized = (price - p.OpenPrice) * p.Volume * contractSize
	} else {
		realized = (p.OpenPrice - price) * p.Volume * contractSize
	}
	// Track win/loss statistics and consecutive streaks
	isWin := realized > 0
	if isWin {
		s.winningTrades++
	} else {
		s.losingTrades++
	}
	s.updateConsecutiveStreaks(isWin)

	// Create closed trade record
	ct := ClosedTrade{
		Ticket: p.Ticket, Symbol: p.Symbol, Type: p.Type, Volume: p.Volume,
		OpenPrice: p.OpenPrice, ClosePrice: price,
		OpenTime: s.openTimes[p.Ticket], CloseTime: s.nowMs, Profit: realized,
	}
	s.closedTrades = append(s.closedTrades, ct)
	delete(s.openTimes, p.Ticket)

	// remove
	s.positions = append(s.positions[:idx], s.positions[idx+1:]...)
	s.balance += realized
	s.equity = s.balance
	s.freeMargin = s.equity
	return &GenericResponseType{Status: 1, Message: "closed"}, nil
}

func (s *SimulatedClient) ModifyTrade(ctx context.Context, in *ModifyTradeRequest, opts ...grpc.CallOption) (*GenericResponseType, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	for i := range s.positions {
		if s.positions[i].Ticket == in.Ticket {
			if in.StopLoss != 0 {
				s.positions[i].StopLoss = in.StopLoss
			}
			if in.TakeProfit != 0 {
				s.positions[i].TakeProfit = in.TakeProfit
			}
			return &GenericResponseType{Status: 1, Message: "modified"}, nil
		}
	}
	return &GenericResponseType{Status: 0, Message: "ticket not found"}, nil
}

func (s *SimulatedClient) PlaceTrade(ctx context.Context, in *PlaceTradeRequest, opts ...grpc.CallOption) (*PlaceTradeResponse, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Prevent trading if account is blown out
	if s.isBlownOut {
		return &PlaceTradeResponse{Status: 0, Message: "account blown out"}, nil
	}

	// Check if there's sufficient free margin (simplified check)
	if s.freeMargin <= 0 {
		return &PlaceTradeResponse{Status: 0, Message: "insufficient margin"}, nil
	}

	s.lastTicket++
	s.totalTrades++
	t := s.lastTicket
	open := s.currentPrice
	if in.Price != nil && *in.Price != 0 {
		open = *in.Price
	}
	p := AccountPositionType{
		Ticket:       t,
		Symbol:       in.Symbol,
		Type:         in.ActionType,
		Volume:       in.Volume,
		OpenPrice:    open,
		CurrentPrice: s.currentPrice,
		StopLoss:     0,
		TakeProfit:   0,
		Magic:        0,
	}

	if in.StopLoss != nil {
		p.StopLoss = *in.StopLoss
	}
	if in.TakeProfit != nil {
		p.TakeProfit = *in.TakeProfit
	}
	// fmt.Println("\n\n Placing Trade:", &p, "\n\n ")
	s.positions = append(s.positions, p)
	if s.openTimes == nil {
		s.openTimes = make(map[int64]int64)
	}
	s.openTimes[t] = s.nowMs
	ticketStr := fmt.Sprintf("%d", t)
	return &PlaceTradeResponse{Status: 1, Message: "placed", Ticket: &ticketStr}, nil
}

func (s *SimulatedClient) ManageSymbol(ctx context.Context, in *ManageSymbolRequest, opts ...grpc.CallOption) (*ManageSymbolResponse, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Log symbol management for debugging
	fmt.Printf("ManageSymbol called for symbol: %s\n", in.Symbol)

	return &ManageSymbolResponse{
		Status:  1,
		Message: fmt.Sprintf("Symbol %s managed successfully", in.Symbol),
	}, nil
}

func (s *SimulatedClient) GetTerminalError(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GenericResponseType, error) {
	return &GenericResponseType{Status: 0, Message: "simulation"}, nil
}

func (s *SimulatedClient) GetSymbolTick(ctx context.Context, in *GetSymbolTickRequest, opts ...grpc.CallOption) (*TickType, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	return &TickType{Ask: s.currentPrice + s.spread/2, Bid: s.currentPrice - s.spread/2, Time: s.nowMs}, nil
}

// --- Backtest runner ---

type BacktestBar struct {
	Time   int64    `json:"time"`
	Open   float64  `json:"open"`
	High   float64  `json:"high"`
	Low    float64  `json:"low"`
	Close  float64  `json:"close"`
	Volume float64  `json:"volume"`
	Spread *float32 `json:"spread,omitempty"`
}

// RunBacktestFromFile replays OHLC bars as a sequence of ticks: O->H/L->L/H->C per bar.
func RunBacktestFromFile(sim *SimulatedClient, filepath string, tickRateMs int64, cb Callback, opts Options) error {
	f, err := os.Open(filepath)
	if err != nil {
		return err
	}
	defer f.Close()
	var bars []BacktestBar
	if err := json.NewDecoder(f).Decode(&bars); err != nil {
		return err
	}
	// ensure ascending by time
	sort.Slice(bars, func(i, j int) bool { return bars[i].Time < bars[j].Time })

	startEquity := sim.equity
	start := time.Now()

	// println("Running backtest with", len(bars), "bars...")

	for _, b := range bars {
		if State.Is_paused {
			time.Sleep(10 * time.Millisecond)
			continue
		}
		// determine traversal order to guarantee high & low touched deterministically
		path := []float64{b.Open}
		if b.Close >= b.Open {
			path = append(path, b.High, b.Low, b.Close)
		} else {
			path = append(path, b.Low, b.High, b.Close)
		}
		for i, px := range path {
			mid := float32(px)
			spread := sim.spread
			if b.Spread != nil {
				spread = *b.Spread
			}
			ts := b.Time
			if i > 0 {
				ts += int64(i) * tickRateMs / 4
			}
			sim.UpdatePrice(mid, ts)
			tick := &TickType{Ask: mid + spread/2, Bid: mid - spread/2, Time: ts}
			lines := cb(opts, tick)
			appendLogs(lines)
		}
	}

	// end-of-run report
	dur := time.Since(start)
	totalTrades := len(sim.closedTrades)
	winRate := float64(0)
	if totalTrades > 0 {
		winRate = float64(sim.winningTrades) / float64(totalTrades) * 100
	}

	// Calculate average win/loss
	var totalWins, totalLosses float32
	var avgWin, avgLoss float32
	for _, trade := range sim.closedTrades {
		if trade.Profit > 0 {
			totalWins += trade.Profit
		} else {
			totalLosses += trade.Profit
		}
	}
	if sim.winningTrades > 0 {
		avgWin = totalWins / float32(sim.winningTrades)
	}
	if sim.losingTrades > 0 {
		avgLoss = totalLosses / float32(sim.losingTrades)
	}

	// Calculate unrealized P&L for open positions
	unrealizedPnL := float32(0)
	for _, p := range sim.positions {
		contractSize := sim.getContractSize(p.Symbol) // Symbol-specific contract size
		if p.Type == 0 {                              // buy
			unrealizedPnL += (sim.currentPrice - p.OpenPrice) * p.Volume * contractSize
		} else { // sell
			unrealizedPnL += (p.OpenPrice - sim.currentPrice) * p.Volume * contractSize
		}
	}

	report := []string{
		"\n\n====== Enhanced Backtest Report ======\n\n",
		fmt.Sprintf("Total trades placed: %d \n", sim.totalTrades),
		fmt.Sprintf("Total Bars Fetched: %d \n", len(bars)),
		fmt.Sprintf("Trades closed: %d \n", totalTrades),
		fmt.Sprintf("Winning trades: %d \n", sim.winningTrades),
		fmt.Sprintf("Losing trades: %d \n", sim.losingTrades),
		fmt.Sprintf("Win rate: %.1f%% \n", winRate),
		fmt.Sprintf("Average win: $%.2f \n", avgWin),
		fmt.Sprintf("Average loss: $%.2f \n", avgLoss),
		fmt.Sprintf("Max consecutive wins: %d \n", sim.maxWinStreak),
		fmt.Sprintf("Max consecutive losses: %d \n", sim.maxLossStreak),
		fmt.Sprintf("Current win streak: %d \n", sim.currentWinStreak),
		fmt.Sprintf("Current loss streak: %d \n", sim.currentLossStreak),
		fmt.Sprintf("Start equity: $%.2f \n", startEquity),
		fmt.Sprintf("End equity: $%.2f \n", sim.equity),
		fmt.Sprintf("Realized P&L: $%.2f \n", sim.balance-startEquity),
		fmt.Sprintf("Unrealized P&L: $%.2f \n", unrealizedPnL),
		fmt.Sprintf("Total P&L: $%.2f \n", sim.equity-startEquity),
		fmt.Sprintf("Max drawdown: $%.2f (%.1f%%) \n", sim.maxDrawdown, sim.maxDrawdown/startEquity*100),
		fmt.Sprintf("Account status: %s \n", func() string {
			if sim.isBlownOut {
				return "BLOWN OUT"
			}
			return "ACTIVE"
		}()),
		fmt.Sprintf("Duration: %s \n", dur.String()),
		fmt.Sprintf("Open positions: %d \n\n", len(sim.positions)),
	}
	maxShow := 50
	if l := len(sim.closedTrades); l > 0 {
		from := l - maxShow
		if from < 0 {
			from = 0
		}
		report = append(report, fmt.Sprintf("Recent %d closed trades:\n\n", l-from))
		for _, ct := range sim.closedTrades[from:] {
			report = append(report, fmt.Sprintf("#%d %s %d vol=%.2f open=%.5f close=%.5f pnl=%.2f\n", ct.Ticket, ct.Symbol, ct.Type, ct.Volume, ct.OpenPrice, ct.ClosePrice, ct.Profit))
		}
		report = append(report, "\n\n")

	}

	if l := len(sim.positions); l > 0 {
		report = append(report, fmt.Sprintf("Open Positions:\n\n"))
		for _, p := range sim.positions {
			// Calculate current unrealized P&L for this position
			var unrealizedPnL float32
			contractSize := sim.getContractSize(p.Symbol) // Symbol-specific contract size
			if p.Type == 0 {                              // buy
				unrealizedPnL = (sim.currentPrice - p.OpenPrice) * p.Volume * contractSize
			} else { // sell
				unrealizedPnL = (p.OpenPrice - sim.currentPrice) * p.Volume * contractSize
			}

			tradeType := "BUY"
			if p.Type == 1 {
				tradeType = "SELL"
			}

			report = append(report, fmt.Sprintf("#%d %s %s vol=%.2f \n open=%.5f current=%.5f \n unrealized_pnl=$%.2f \n stop_loss=%.5f take_profit=%.5f\n\n",
				p.Ticket, p.Symbol, tradeType, p.Volume, p.OpenPrice, sim.currentPrice, unrealizedPnL, p.StopLoss, p.TakeProfit))
		}
		report = append(report, "\n")
	}

	fmt.Println("\n", report, "\n ")
	appendLogs(report)

	return nil
}
