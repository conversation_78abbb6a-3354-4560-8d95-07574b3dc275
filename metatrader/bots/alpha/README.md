# Alpha Bot

An ensemble multi-strategy trading bot for remote-mt5. It combines trend momentum, volatility breakout, carry (FX), and mean-reversion with regime filters, volatility targeting, and strict risk controls.

## Research process
- Surveyed academic and practitioner literature (momentum, volatility targeting, Donchian/Turtle rules, FX carry) and distilled robust, low-parameter components.
- Designed an ensemble that z-score normalizes signals and weights them with caps and thresholds.
- Integrated FR-6 risk controls and execution management per the project conventions.

## Config
Minimal required keys in `env.json`:
- `symbol`: primary instrument (e.g., "EURUSD")
- `rate`: tick frequency when `remote_ticking` is false; still required for bridge API
- `remote_ticking`: set true to stream ticks from the terminal
- `min_trade_interval_ms`: throttle between orders

Recommended:
- `data.ohlc.timeframe` (e.g., M5), `data.ohlc.warmup_bars`
- `data.volatility.ewma_lambda`, `data.volatility.min_vol`
- `sizing.target_vol_annualized`, `portfolio.per_symbol_lot_cap`
- `execution.stop_loss.mode="atr"`, `execution.stop_loss.atr_multiple`, `execution.take_profit.rr_multiple`
- `risk.*` controls as needed

See `env.json` for a complete, documented example.

## Run
Launched by node-manager like other bots; it needs `-rpc_uri`, `-ws_uri`, `-token`, `-id`, and `-env_file`.

Local build test (optional):
```bash
cd metatrader/bots/alpha
go build ./...
```

If running standalone for smoke tests (optional), ensure the bridge is reachable and pass the flags; otherwise, node-manager will wire these up in deployment.

## Notes
- The bot guards against nil ticks and warms up indicators before trading.
- Volatility targeting scales lots by the EWMA of bar returns relative to a target annualized volatility.
