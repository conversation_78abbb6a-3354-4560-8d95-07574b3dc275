#!/usr/bin/env python3
import json
import subprocess
import re
import time
from datetime import datetime, timedelta
import os

# Test configurations with focus on risk management
TIMEFRAMES = ["M5", "M15", "M30"]  # Removed M1 to reduce noise
MONTHS_BACK = [1, 2, 3]  # Shorter periods for better risk assessment
SYMBOLS = ["EURUSD", "GBPUSD"]  # Removed USDJPY due to extreme volatility

# Risk-focused parameter sets
PARAM_SETS = [
    # Ultra Conservative
    {
        "risk_per_trade_pct": 0.5,
        "max_position_size_pct": 2.0,
        "atr_stop_multiple": 3.0,
        "trend_threshold": 0.002,
        "rsi_lower": 40, "rsi_upper": 60,
        "ensemble_threshold": 0.4,
        "max_daily_trades": 5
    },
    # Conservative
    {
        "risk_per_trade_pct": 1.0,
        "max_position_size_pct": 3.0,
        "atr_stop_multiple": 2.5,
        "trend_threshold": 0.0015,
        "rsi_lower": 35, "rsi_upper": 65,
        "ensemble_threshold": 0.3,
        "max_daily_trades": 8
    },
    # Moderate
    {
        "risk_per_trade_pct": 1.5,
        "max_position_size_pct": 5.0,
        "atr_stop_multiple": 2.0,
        "trend_threshold": 0.001,
        "rsi_lower": 30, "rsi_upper": 70,
        "ensemble_threshold": 0.25,
        "max_daily_trades": 12
    },
    # Balanced
    {
        "risk_per_trade_pct": 2.0,
        "max_position_size_pct": 7.0,
        "atr_stop_multiple": 1.8,
        "trend_threshold": 0.0008,
        "rsi_lower": 32, "rsi_upper": 68,
        "ensemble_threshold": 0.2,
        "max_daily_trades": 15
    },
    # Aggressive (but still risk-managed)
    {
        "risk_per_trade_pct": 2.5,
        "max_position_size_pct": 8.0,
        "atr_stop_multiple": 1.5,
        "trend_threshold": 0.0005,
        "rsi_lower": 25, "rsi_upper": 75,
        "ensemble_threshold": 0.15,
        "max_daily_trades": 20
    }
]

def get_timestamp_months_back(months):
    """Get timestamp for N months back"""
    now = datetime.now()
    past = now - timedelta(days=months * 30)
    return int(past.timestamp() * 1000)

def update_env_config(symbol, timeframe, months_back, params):
    """Update env.backtest.json with test parameters"""
    with open('env.backtest.json', 'r') as f:
        config = json.load(f)
    
    # Update basic settings
    config['symbol'] = symbol
    config['data']['ohlc']['timeframe'] = timeframe
    config['backtest_timeframe'] = timeframe
    config['backtest_from'] = get_timestamp_months_back(months_back)
    config['backtest_count'] = 1500 if timeframe == "M5" else 1000 if timeframe == "M15" else 800
    
    # Update risk management parameters
    config['risk']['risk_per_trade_pct'] = params['risk_per_trade_pct']
    config['risk']['max_position_size_pct'] = params['max_position_size_pct']
    config['risk']['atr_stop_multiple'] = params['atr_stop_multiple']
    config['risk']['max_daily_trades'] = params['max_daily_trades']
    
    # Update strategy parameters
    config['strategies']['trend_momentum']['threshold'] = params['trend_threshold']
    config['strategies']['mean_reversion']['rsi_lower'] = params['rsi_lower']
    config['strategies']['mean_reversion']['rsi_upper'] = params['rsi_upper']
    config['ensemble']['threshold'] = params['ensemble_threshold']
    
    with open('env.backtest.json', 'w') as f:
        json.dump(config, f, indent=2)

def run_backtest():
    """Run a single backtest and extract results"""
    try:
        result = subprocess.run([
            'go', 'run', '.', 
            '-auth_token', 'test_terminal_token',
            '-id', 'alpha-risk-opt',
            '-rpc_uri', '0.0.0.0:50050',
            '-ws_uri', 'ws://localhost:4010/bots',
            '-env_file', './env.backtest.json'
        ], capture_output=True, text=True, timeout=90)
        
        output = result.stdout + result.stderr
        
        # Extract metrics from enhanced report
        pnl_match = re.search(r'Total P&L: \$([-+]?\d+\.?\d*)', output)
        equity_match = re.search(r'End equity: \$([\d.]+)', output)
        drawdown_match = re.search(r'Max drawdown: \$([\d.]+)', output)
        trades_match = re.search(r'Trades closed: (\d+)', output)
        win_rate_match = re.search(r'Win rate: ([\d.]+)%', output)
        blown_match = re.search(r'Account status: (BLOWN OUT|ACTIVE)', output)
        
        pnl = float(pnl_match.group(1)) if pnl_match else 0.0
        equity = float(equity_match.group(1)) if equity_match else 1000.0
        drawdown = float(drawdown_match.group(1)) if drawdown_match else 0.0
        trades = int(trades_match.group(1)) if trades_match else 0
        win_rate = float(win_rate_match.group(1)) if win_rate_match else 0.0
        is_blown = blown_match.group(1) == "BLOWN OUT" if blown_match else False
        
        # Calculate risk-adjusted return (Sharpe-like ratio)
        risk_adjusted_return = 0.0
        if drawdown > 0:
            risk_adjusted_return = pnl / drawdown
        elif pnl > 0:
            risk_adjusted_return = pnl / 100  # Assume minimum risk
        
        return {
            'pnl': pnl,
            'equity': equity,
            'drawdown': drawdown,
            'trades': trades,
            'win_rate': win_rate,
            'is_blown': is_blown,
            'risk_adjusted_return': risk_adjusted_return,
            'success': True,
            'output': output[-500:]  # Last 500 chars for debugging
        }
    except Exception as e:
        return {
            'pnl': -1000.0,  # Penalty for failed runs
            'equity': 0.0,
            'drawdown': 1000.0,
            'trades': 0,
            'win_rate': 0.0,
            'is_blown': True,
            'risk_adjusted_return': -10.0,
            'success': False,
            'error': str(e)
        }

def main():
    results = []
    test_count = 0
    total_tests = len(SYMBOLS) * len(TIMEFRAMES) * len(MONTHS_BACK) * len(PARAM_SETS)
    
    print(f"Starting RISK-MANAGED alpha bot optimization with {total_tests} test combinations...")
    
    for symbol in SYMBOLS:
        for timeframe in TIMEFRAMES:
            for months_back in MONTHS_BACK:
                for i, params in enumerate(PARAM_SETS):
                    test_count += 1
                    print(f"\n[{test_count}/{total_tests}] Testing: {symbol} {timeframe} {months_back}M Risk-Set{i+1}")
                    print(f"Risk: {params['risk_per_trade_pct']}% per trade, Max pos: {params['max_position_size_pct']}%, ATR stop: {params['atr_stop_multiple']}x")
                    
                    # Update configuration
                    update_env_config(symbol, timeframe, months_back, params)
                    
                    # Run backtest
                    result = run_backtest()
                    
                    # Store results
                    test_result = {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'months_back': months_back,
                        'param_set': i + 1,
                        'params': params,
                        **result
                    }
                    results.append(test_result)
                    
                    status = "BLOWN" if result['is_blown'] else "ACTIVE"
                    print(f"Result: P&L=${result['pnl']:.2f}, Trades={result['trades']}, Win%={result['win_rate']:.1f}, Status={status}")
                    
                    # Brief pause between tests
                    time.sleep(0.5)
    
    # Sort by risk-adjusted return first, then by P&L
    results.sort(key=lambda x: (not x['is_blown'], x['risk_adjusted_return'], x['pnl']), reverse=True)
    
    # Save detailed results
    with open('optimization_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Print top 10 results (non-blown accounts only)
    print("\n" + "="*80)
    print("TOP 10 RISK-MANAGED ALPHA BOT CONFIGURATIONS:")
    print("="*80)
    
    non_blown_results = [r for r in results if not r['is_blown']]
    
    for i, result in enumerate(non_blown_results[:10]):
        if result['success']:
            print(f"\n#{i+1} - P&L: ${result['pnl']:.2f} | Risk-Adj Return: {result['risk_adjusted_return']:.2f}")
            print(f"Symbol: {result['symbol']}, Timeframe: {result['timeframe']}, Period: {result['months_back']} months")
            print(f"Risk: {result['params']['risk_per_trade_pct']}% per trade, Max pos: {result['params']['max_position_size_pct']}%")
            print(f"ATR stop: {result['params']['atr_stop_multiple']}x, Trend: {result['params']['trend_threshold']:.4f}")
            print(f"Trades: {result['trades']}, Win Rate: {result['win_rate']:.1f}%, Max DD: ${result['drawdown']:.2f}")
    
    # Find best non-blown configuration and update env.backtest.json
    if non_blown_results and non_blown_results[0]['success']:
        best = non_blown_results[0]
        print(f"\n" + "="*80)
        print("UPDATING env.backtest.json WITH BEST RISK-MANAGED CONFIGURATION:")
        print("="*80)
        update_env_config(best['symbol'], best['timeframe'], best['months_back'], best['params'])
        print(f"Updated with: {best['symbol']} {best['timeframe']} (P&L: ${best['pnl']:.2f}, Risk-Adj: {best['risk_adjusted_return']:.2f})")
    else:
        print("\nWARNING: All configurations resulted in blown accounts! Need more conservative parameters.")

if __name__ == "__main__":
    main()
