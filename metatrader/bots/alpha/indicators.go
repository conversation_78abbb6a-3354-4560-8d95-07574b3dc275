package main

import "math"

// SMA - simple moving average
type SMA struct {
	window int
	q      []float64
	sum    float64
}

func NewSMA(n int) *SMA { return &SMA{window: n, q: make([]float64, 0, n)} }

func (s *SMA) Add(x float64) float64 {
	if s.window <= 1 {
		return x
	}
	if len(s.q) == s.window {
		s.sum -= s.q[0]
		s.q = s.q[1:]
	}
	s.q = append(s.q, x)
	s.sum += x
	return s.Value()
}

func (s *SMA) Value() float64 {
	if len(s.q) == 0 {
		return 0
	}
	return s.sum / float64(len(s.q))
}

// EMA - exponential moving average
type EMA struct {
	alpha float64
	val   float64
	init  bool
}

func NewEMA(n int) *EMA {
	if n <= 1 {
		n = 1
	}
	alpha := 2.0 / (float64(n) + 1.0)
	return &EMA{alpha: alpha}
}

func (e *EMA) Add(x float64) float64 {
	if !e.init {
		e.val = x
		e.init = true
		return e.val
	}
	e.val = e.alpha*x + (1-e.alpha)*e.val
	return e.val
}

func (e *EMA) Value() float64 {
	if !e.init {
		return 0
	}
	return e.val
}

// ATR - Wilder's ATR with smoothing
// Feed with high, low, close

type ATR struct {
	period int
	ema    *EMA
	prevC  float64
	init   bool
}

func NewATR(period int) *ATR { return &ATR{period: period, ema: NewEMA(period)} }

func (a *ATR) Add(h, l, c float64) float64 {
	var tr float64
	if !a.init {
		tr = h - l
		a.prevC = c
		a.init = true
		return a.ema.Add(tr)
	}
	tr1 := h - l
	tr2 := math.Abs(h - a.prevC)
	tr3 := math.Abs(l - a.prevC)
	tr = math.Max(tr1, math.Max(tr2, tr3))
	a.prevC = c
	return a.ema.Add(tr)
}

func (a *ATR) Value() float64 { return a.ema.Value() }
