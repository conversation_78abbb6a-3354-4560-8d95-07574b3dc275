package main

import (
	. "bridge"
	"fmt"
	"math"
	"strings"
	"time"
)

type tickLogger struct {
	lines []string
	max   int
}

func newTickLogger(max int) *tickLogger { return &tickLogger{lines: make([]string, 0, 128), max: max} }

func (l *tickLogger) Log(args ...interface{}) {
	// fmt.Println("tickLogger.Log:", args)
	if l == nil {
		return
	}
	msg := fmt.Sprintln(args...)
	l.lines = append(l.lines, strings.TrimRight(msg, "\n"))
	if len(l.lines) > l.max {
		l.lines = l.lines[len(l.lines)-l.max:]
	}
}

type Config struct {
	Symbol                string
	Rate                  float64
	Remote_ticking        bool
	Currency_maps         []string
	Dry_run               bool
	Min_trade_interval_ms int
	Trading_mode          string // "forex", "gold", "volatility_index"

	Scalping struct {
		Timeframe          string  // M1, M5
		Fast_ema_period    int     // 5
		Slow_ema_period    int     // 21
		Rsi_period         int     // 14
		Rsi_oversold       float64 // 30
		Rsi_overbought     float64 // 70
		Bb_period          int     // 20
		Bb_deviation       float64 // 2.0
		Min_pip_move       float64 // 0.0005 for EURUSD
		Max_spread_pips    float64 // 2.0
		Quick_profit_pips  float64 // 3-5 pips
		Stop_loss_pips     float64 // 8-10 pips
		Risk_per_trade_pct float64 // 1%
		Fixed_volume       float32 // 0.01
	}

	Advanced_indicators struct {
		Use_macd           bool
		Macd_fast_period   int // 12
		Macd_slow_period   int // 26
		Macd_signal_period int // 9

		Use_stochastic   bool
		Stoch_k_period   int     // 14
		Stoch_d_period   int     // 3
		Stoch_smooth     int     // 3
		Stoch_oversold   float64 // 20
		Stoch_overbought float64 // 80

		Use_williams_r      bool
		Williams_period     int     // 14
		Williams_oversold   float64 // -80
		Williams_overbought float64 // -20

		Use_parabolic_sar bool
		Sar_step          float64 // 0.02
		Sar_maximum       float64 // 0.2

		Use_atr        bool
		Atr_period     int     // 14
		Atr_multiplier float64 // 2.0
	}

	Strategies struct {
		Multi_indicator_weight float64 // Weight for multi-indicator strategy
		Momentum_weight        float64 // Weight for momentum strategy
		Mean_reversion_weight  float64 // Weight for mean reversion strategy
		Trend_following_weight float64 // Weight for trend following strategy
		Min_signal_strength    float64 // Minimum combined signal strength to trade
	}

	Asset_specific struct {
		Forex struct {
			Pip_value_override map[string]float64 // Custom pip values for specific pairs
		}
		Gold struct {
			Pip_value         float64 // 0.1 for XAUUSD
			Volatility_filter float64 // Minimum ATR for trading
		}
		Volatility_index struct {
			Pip_value  float64 // 0.01 for most volatility indices
			Index_type string  // "10", "25", "50", "75", "100"
			Tick_size  float64 // Minimum price movement
		}
	}

	Risk struct {
		Max_concurrent_trades    int
		Max_daily_drawdown_pct   float64
		Max_daily_trades         int
		Time_windows             []string
		Stop_trading_on_loss_pct float64
	}

	Execution struct {
		Max_slippage_points int
		Magic_base          int
	}
}

var config Map = Map{}

// Asset type constants
const (
	TRADING_MODE_FOREX            = "forex"
	TRADING_MODE_GOLD             = "gold"
	TRADING_MODE_VOLATILITY_INDEX = "volatility_index"
)

// Calculate pip value based on trading mode and symbol
func calculatePipValue(symbol string, tradingMode string, c Config) float64 {
	symbol = strings.ToUpper(symbol)

	switch tradingMode {
	case TRADING_MODE_GOLD:
		if c.Asset_specific.Gold.Pip_value > 0 {
			return c.Asset_specific.Gold.Pip_value
		}
		if strings.Contains(symbol, "XAU") || strings.Contains(symbol, "GOLD") {
			return 0.1 // Standard pip value for gold
		}
		return 0.01 // Fallback for other precious metals

	case TRADING_MODE_VOLATILITY_INDEX:
		if c.Asset_specific.Volatility_index.Pip_value > 0 {
			return c.Asset_specific.Volatility_index.Pip_value
		}
		// Volatility indices typically use 0.01 as pip value
		return 0.01

	case TRADING_MODE_FOREX:
		fallthrough
	default:
		// Check for custom pip value override
		if c.Asset_specific.Forex.Pip_value_override != nil {
			if customPip, exists := c.Asset_specific.Forex.Pip_value_override[symbol]; exists {
				return customPip
			}
		}

		// Standard forex pip calculation
		if strings.Contains(symbol, "JPY") {
			return 0.01 // JPY pairs use 0.01
		}
		return 0.0001 // Most forex pairs use 0.0001
	}
}

// Determine trading mode from symbol if not explicitly set
func determineTradingMode(symbol string, configMode string) string {
	if configMode != "" {
		return configMode
	}

	symbol = strings.ToUpper(symbol)

	// Gold detection
	if strings.Contains(symbol, "XAU") || strings.Contains(symbol, "GOLD") {
		return TRADING_MODE_GOLD
	}

	// Volatility index detection
	if strings.Contains(symbol, "VIX") ||
		strings.HasPrefix(symbol, "VOLATILITY") ||
		strings.HasPrefix(symbol, "VOL") {
		return TRADING_MODE_VOLATILITY_INDEX
	}

	// Default to forex
	return TRADING_MODE_FOREX
}

// Scalping indicators
type EMA struct {
	period int
	alpha  float64
	value  float64
	init   bool
}

func NewEMA(period int) *EMA {
	return &EMA{period: period, alpha: 2.0 / (float64(period) + 1.0)}
}

func (e *EMA) Add(price float64) {
	if !e.init {
		e.value = price
		e.init = true
	} else {
		e.value = e.alpha*price + (1-e.alpha)*e.value
	}
}

func (e *EMA) Value() float64 { return e.value }

type RSI struct {
	period    int
	gains     []float64
	losses    []float64
	avgGain   float64
	avgLoss   float64
	lastPrice float64
	init      bool
}

func NewRSI(period int) *RSI {
	return &RSI{period: period}
}

func (r *RSI) Add(price float64) {
	if !r.init {
		r.lastPrice = price
		r.init = true
		return
	}

	change := price - r.lastPrice
	gain := math.Max(change, 0)
	loss := math.Max(-change, 0)

	r.gains = append(r.gains, gain)
	r.losses = append(r.losses, loss)

	if len(r.gains) > r.period {
		r.gains = r.gains[1:]
		r.losses = r.losses[1:]
	}

	if len(r.gains) == r.period {
		sumGains := 0.0
		sumLosses := 0.0
		for i := 0; i < r.period; i++ {
			sumGains += r.gains[i]
			sumLosses += r.losses[i]
		}
		r.avgGain = sumGains / float64(r.period)
		r.avgLoss = sumLosses / float64(r.period)
	}

	r.lastPrice = price
}

func (r *RSI) Value() float64 {
	if r.avgLoss == 0 {
		return 100
	}
	rs := r.avgGain / r.avgLoss
	return 100 - (100 / (1 + rs))
}

type BollingerBands struct {
	period    int
	deviation float64
	prices    []float64
	sma       float64
	upper     float64
	lower     float64
}

func NewBollingerBands(period int, deviation float64) *BollingerBands {
	return &BollingerBands{period: period, deviation: deviation}
}

func (bb *BollingerBands) Add(price float64) {
	bb.prices = append(bb.prices, price)
	if len(bb.prices) > bb.period {
		bb.prices = bb.prices[1:]
	}

	if len(bb.prices) == bb.period {
		sum := 0.0
		for _, p := range bb.prices {
			sum += p
		}
		bb.sma = sum / float64(bb.period)

		variance := 0.0
		for _, p := range bb.prices {
			variance += math.Pow(p-bb.sma, 2)
		}
		stdDev := math.Sqrt(variance / float64(bb.period))

		bb.upper = bb.sma + bb.deviation*stdDev
		bb.lower = bb.sma - bb.deviation*stdDev
	}
}

func (bb *BollingerBands) Upper() float64  { return bb.upper }
func (bb *BollingerBands) Lower() float64  { return bb.lower }
func (bb *BollingerBands) Middle() float64 { return bb.sma }

// MACD Indicator
type MACD struct {
	fastEMA   *EMA
	slowEMA   *EMA
	signalEMA *EMA
	macdLine  float64
	signal    float64
	histogram float64
	init      bool
}

func NewMACD(fastPeriod, slowPeriod, signalPeriod int) *MACD {
	return &MACD{
		fastEMA:   NewEMA(fastPeriod),
		slowEMA:   NewEMA(slowPeriod),
		signalEMA: NewEMA(signalPeriod),
	}
}

func (m *MACD) Add(price float64) {
	m.fastEMA.Add(price)
	m.slowEMA.Add(price)

	if m.fastEMA.init && m.slowEMA.init {
		m.macdLine = m.fastEMA.Value() - m.slowEMA.Value()
		m.signalEMA.Add(m.macdLine)

		if m.signalEMA.init {
			m.signal = m.signalEMA.Value()
			m.histogram = m.macdLine - m.signal
			m.init = true
		}
	}
}

func (m *MACD) MACD() float64      { return m.macdLine }
func (m *MACD) Signal() float64    { return m.signal }
func (m *MACD) Histogram() float64 { return m.histogram }
func (m *MACD) IsInit() bool       { return m.init }

// Stochastic Oscillator
type Stochastic struct {
	kPeriod  int
	dPeriod  int
	smooth   int
	highs    []float64
	lows     []float64
	closes   []float64
	kValues  []float64
	kPercent float64
	dPercent float64
	init     bool
}

func NewStochastic(kPeriod, dPeriod, smooth int) *Stochastic {
	return &Stochastic{
		kPeriod: kPeriod,
		dPeriod: dPeriod,
		smooth:  smooth,
	}
}

func (s *Stochastic) Add(high, low, close float64) {
	s.highs = append(s.highs, high)
	s.lows = append(s.lows, low)
	s.closes = append(s.closes, close)

	if len(s.highs) > s.kPeriod {
		s.highs = s.highs[1:]
		s.lows = s.lows[1:]
		s.closes = s.closes[1:]
	}

	if len(s.highs) == s.kPeriod {
		highestHigh := s.highs[0]
		lowestLow := s.lows[0]

		for i := 1; i < s.kPeriod; i++ {
			if s.highs[i] > highestHigh {
				highestHigh = s.highs[i]
			}
			if s.lows[i] < lowestLow {
				lowestLow = s.lows[i]
			}
		}

		if highestHigh != lowestLow {
			rawK := ((close - lowestLow) / (highestHigh - lowestLow)) * 100
			s.kValues = append(s.kValues, rawK)

			if len(s.kValues) > s.smooth {
				s.kValues = s.kValues[1:]
			}

			if len(s.kValues) == s.smooth {
				sum := 0.0
				for _, k := range s.kValues {
					sum += k
				}
				s.kPercent = sum / float64(s.smooth)
				s.init = true
			}
		}
	}
}

func (s *Stochastic) K() float64   { return s.kPercent }
func (s *Stochastic) D() float64   { return s.dPercent }
func (s *Stochastic) IsInit() bool { return s.init }

// Williams %R
type WilliamsR struct {
	period int
	highs  []float64
	lows   []float64
	value  float64
	init   bool
}

func NewWilliamsR(period int) *WilliamsR {
	return &WilliamsR{period: period}
}

func (w *WilliamsR) Add(high, low, close float64) {
	w.highs = append(w.highs, high)
	w.lows = append(w.lows, low)

	if len(w.highs) > w.period {
		w.highs = w.highs[1:]
		w.lows = w.lows[1:]
	}

	if len(w.highs) == w.period {
		highestHigh := w.highs[0]
		lowestLow := w.lows[0]

		for i := 1; i < w.period; i++ {
			if w.highs[i] > highestHigh {
				highestHigh = w.highs[i]
			}
			if w.lows[i] < lowestLow {
				lowestLow = w.lows[i]
			}
		}

		if highestHigh != lowestLow {
			w.value = ((highestHigh - close) / (highestHigh - lowestLow)) * -100
			w.init = true
		}
	}
}

func (w *WilliamsR) Value() float64 { return w.value }
func (w *WilliamsR) IsInit() bool   { return w.init }

// Parabolic SAR
type ParabolicSAR struct {
	step     float64
	maximum  float64
	sar      float64
	ep       float64
	af       float64
	isLong   bool
	init     bool
	prevHigh float64
	prevLow  float64
}

func NewParabolicSAR(step, maximum float64) *ParabolicSAR {
	return &ParabolicSAR{
		step:    step,
		maximum: maximum,
		af:      step,
	}
}

func (p *ParabolicSAR) Add(high, low, close float64) {
	if !p.init {
		p.sar = low
		p.ep = high
		p.isLong = true
		p.init = true
		p.prevHigh = high
		p.prevLow = low
		return
	}

	if p.isLong {
		if high > p.ep {
			p.ep = high
			p.af = math.Min(p.af+p.step, p.maximum)
		}

		newSAR := p.sar + p.af*(p.ep-p.sar)

		if newSAR > low || newSAR > p.prevLow {
			p.isLong = false
			p.sar = p.ep
			p.ep = low
			p.af = p.step
		} else {
			p.sar = newSAR
		}
	} else {
		if low < p.ep {
			p.ep = low
			p.af = math.Min(p.af+p.step, p.maximum)
		}

		newSAR := p.sar + p.af*(p.ep-p.sar)

		if newSAR < high || newSAR < p.prevHigh {
			p.isLong = true
			p.sar = p.ep
			p.ep = high
			p.af = p.step
		} else {
			p.sar = newSAR
		}
	}

	p.prevHigh = high
	p.prevLow = low
}

func (p *ParabolicSAR) Value() float64 { return p.sar }
func (p *ParabolicSAR) IsLong() bool   { return p.isLong }
func (p *ParabolicSAR) IsInit() bool   { return p.init }

// ATR (Average True Range)
type ATR struct {
	period    int
	trueRange []float64
	value     float64
	prevClose float64
	init      bool
}

func NewATR(period int) *ATR {
	return &ATR{period: period}
}

func (a *ATR) Add(high, low, close float64) {
	if !a.init {
		a.prevClose = close
		a.init = true
		return
	}

	tr1 := high - low
	tr2 := math.Abs(high - a.prevClose)
	tr3 := math.Abs(low - a.prevClose)

	tr := math.Max(tr1, math.Max(tr2, tr3))
	a.trueRange = append(a.trueRange, tr)

	if len(a.trueRange) > a.period {
		a.trueRange = a.trueRange[1:]
	}

	if len(a.trueRange) == a.period {
		sum := 0.0
		for _, tr := range a.trueRange {
			sum += tr
		}
		a.value = sum / float64(a.period)
	}

	a.prevClose = close
}

func (a *ATR) Value() float64 { return a.value }
func (a *ATR) IsInit() bool   { return len(a.trueRange) == a.period }

// Bar aggregation
type Bar struct {
	O, H, L, C float64
	T          int64
}

func bucketStart(t time.Time, timeframe string) time.Time {
	switch timeframe {
	case "M1":
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	case "M5":
		min := (t.Minute() / 5) * 5
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), min, 0, 0, t.Location())
	case "M15":
		min := (t.Minute() / 15) * 15
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), min, 0, 0, t.Location())
	default:
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	}
}

func applyCurrencyMaps(symbol string, maps []string) string {
	for _, m := range maps {
		parts := strings.Split(m, ":")
		if len(parts) == 2 && parts[0] == symbol {
			return parts[1]
		}
	}
	return symbol
}

func computeMagic(botId string) int64 {
	hash := int64(0)
	for _, c := range botId {
		hash = hash*31 + int64(c)
	}
	return 900000 + (hash % 100000)
}

func main() {
	tl := newTickLogger(500)
	tl.Log("[gringo] Starting scalping bot...")

	// Trading state
	lastTradeAt := time.Time{}
	var actedBarTs int64 = 0
	dailyTrades := 0
	dayStart := ""

	// Bar aggregation
	var curBar *Bar
	var bars []Bar
	var initialized bool = false

	// Indicators - will be initialized after fetching historical data
	var fastEMA *EMA
	var slowEMA *EMA
	var rsi *RSI
	var bb *BollingerBands

	// Advanced indicators
	var macd *MACD
	var stochastic *Stochastic
	var williamsR *WilliamsR
	var parabolicSAR *ParabolicSAR
	var atr *ATR

	// Function to initialize indicators with historical data
	initializeIndicators := func(o Options, c Config) error {
		if initialized {
			return nil
		}

		symbol := c.Symbol
		if symbol == "" {
			symbol = "EURUSD"
		}
		mappedSymbol := applyCurrencyMaps(symbol, c.Currency_maps)

		timeframe := c.Scalping.Timeframe
		if timeframe == "" {
			timeframe = "M15"
		}

		// In simulation mode, skip historical fetch as it's not needed
		if o.Simulated {
			tl.Log("[gringo] Simulation mode - initializing indicators without historical data")
			fastEMA = NewEMA(c.Scalping.Fast_ema_period)
			slowEMA = NewEMA(c.Scalping.Slow_ema_period)
			rsi = NewRSI(c.Scalping.Rsi_period)
			bb = NewBollingerBands(c.Scalping.Bb_period, c.Scalping.Bb_deviation)

			// Initialize advanced indicators if enabled
			if c.Advanced_indicators.Use_macd {
				macd = NewMACD(c.Advanced_indicators.Macd_fast_period, c.Advanced_indicators.Macd_slow_period, c.Advanced_indicators.Macd_signal_period)
			}
			if c.Advanced_indicators.Use_stochastic {
				stochastic = NewStochastic(c.Advanced_indicators.Stoch_k_period, c.Advanced_indicators.Stoch_d_period, c.Advanced_indicators.Stoch_smooth)
			}
			if c.Advanced_indicators.Use_williams_r {
				williamsR = NewWilliamsR(c.Advanced_indicators.Williams_period)
			}
			if c.Advanced_indicators.Use_parabolic_sar {
				parabolicSAR = NewParabolicSAR(c.Advanced_indicators.Sar_step, c.Advanced_indicators.Sar_maximum)
			}
			if c.Advanced_indicators.Use_atr {
				atr = NewATR(c.Advanced_indicators.Atr_period)
			}

			initialized = true
			return nil
		}

		// Manage symbol before fetching ticks (MT5 API requirement)
		tl.Log("[gringo] Managing symbol:", mappedSymbol)
		_, err := o.Client.ManageSymbol(o.Ctx, &ManageSymbolRequest{
			Symbol: mappedSymbol,
		})
		if err != nil {
			tl.Log("[gringo] Failed to manage symbol:", err)
		}

		// Fetch last 50 candles for indicator warmup (live mode only)
		tl.Log("[gringo] Fetching historical candles for warmup...")
		resp, err := o.Client.GetTicksFrom(o.Ctx, &GetTicksFromRequest{
			Symbol:    mappedSymbol,
			StartDate: time.Now().UnixMilli(),
			Length:    50,
			Timeframe: &timeframe,
		})

		if err != nil || resp == nil || len(resp.Ticks) < 10 {
			tl.Log("[gringo] Failed to fetch historical data, using defaults")
			// Initialize with default periods from config
			fastEMA = NewEMA(c.Scalping.Fast_ema_period)
			slowEMA = NewEMA(c.Scalping.Slow_ema_period)
			rsi = NewRSI(c.Scalping.Rsi_period)
			bb = NewBollingerBands(c.Scalping.Bb_period, c.Scalping.Bb_deviation)

			// Initialize advanced indicators if enabled
			if c.Advanced_indicators.Use_macd {
				macd = NewMACD(c.Advanced_indicators.Macd_fast_period, c.Advanced_indicators.Macd_slow_period, c.Advanced_indicators.Macd_signal_period)
			}
			if c.Advanced_indicators.Use_stochastic {
				stochastic = NewStochastic(c.Advanced_indicators.Stoch_k_period, c.Advanced_indicators.Stoch_d_period, c.Advanced_indicators.Stoch_smooth)
			}
			if c.Advanced_indicators.Use_williams_r {
				williamsR = NewWilliamsR(c.Advanced_indicators.Williams_period)
			}
			if c.Advanced_indicators.Use_parabolic_sar {
				parabolicSAR = NewParabolicSAR(c.Advanced_indicators.Sar_step, c.Advanced_indicators.Sar_maximum)
			}
			if c.Advanced_indicators.Use_atr {
				atr = NewATR(c.Advanced_indicators.Atr_period)
			}

			initialized = true
			return nil
		}

		// Initialize indicators
		fastEMA = NewEMA(c.Scalping.Fast_ema_period)
		slowEMA = NewEMA(c.Scalping.Slow_ema_period)
		rsi = NewRSI(c.Scalping.Rsi_period)
		bb = NewBollingerBands(c.Scalping.Bb_period, c.Scalping.Bb_deviation)

		// Initialize advanced indicators if enabled
		if c.Advanced_indicators.Use_macd {
			macd = NewMACD(c.Advanced_indicators.Macd_fast_period, c.Advanced_indicators.Macd_slow_period, c.Advanced_indicators.Macd_signal_period)
		}
		if c.Advanced_indicators.Use_stochastic {
			stochastic = NewStochastic(c.Advanced_indicators.Stoch_k_period, c.Advanced_indicators.Stoch_d_period, c.Advanced_indicators.Stoch_smooth)
		}
		if c.Advanced_indicators.Use_williams_r {
			williamsR = NewWilliamsR(c.Advanced_indicators.Williams_period)
		}
		if c.Advanced_indicators.Use_parabolic_sar {
			parabolicSAR = NewParabolicSAR(c.Advanced_indicators.Sar_step, c.Advanced_indicators.Sar_maximum)
		}
		if c.Advanced_indicators.Use_atr {
			atr = NewATR(c.Advanced_indicators.Atr_period)
		}

		// Warm up indicators with historical data
		tl.Log("[gringo] Warming up indicators with", len(resp.Ticks), "historical candles")
		for _, candle := range resp.Ticks {
			price := float64(candle.Close)
			high := float64(candle.High)
			low := float64(candle.Low)

			fastEMA.Add(price)
			slowEMA.Add(price)
			rsi.Add(price)
			bb.Add(price)

			// Warm up advanced indicators if enabled
			if macd != nil {
				macd.Add(price)
			}
			if stochastic != nil {
				stochastic.Add(high, low, price)
			}
			if williamsR != nil {
				williamsR.Add(high, low, price)
			}
			if parabolicSAR != nil {
				parabolicSAR.Add(high, low, price)
			}
			if atr != nil {
				atr.Add(high, low, price)
			}

			// Also populate bars array
			bars = append(bars, Bar{
				O: float64(candle.Open),
				H: high,
				L: low,
				C: price,
				T: candle.Time,
			})
		}

		tl.Log("[gringo] Indicators initialized with", len(bars), "historical bars")
		tl.Log("[gringo] Fast EMA:", fastEMA.Value(), "Slow EMA:", slowEMA.Value(), "RSI:", rsi.Value())
		initialized = true
		return nil
	}

	var fn Callback = func(o Options, tick *TickType) []string {
		tl.lines = tl.lines[:0]
		tl.Log("[gringo] callback invoked")

		c := Config{}
		MapToStruct(config, &c)

		// Initialize indicators on first call
		if !initialized {
			if err := initializeIndicators(o, c); err != nil {
				tl.Log("[gringo] Failed to initialize indicators:", err)
			}
		}

		if tick == nil {
			tl.Log("[gringo] tick is nil, returning")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		tl.Log("[gringo] tick:", tick.Time, "ask:", tick.Ask, "bid:", tick.Bid)

		// Throttle trades
		if !lastTradeAt.IsZero() && o.Simulated == false && time.Since(lastTradeAt) < time.Duration(c.Min_trade_interval_ms)*time.Millisecond {
			tl.Log("[gringo] throttled, lastTradeAt:", lastTradeAt.UnixMilli())
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		symbol := c.Symbol
		if symbol == "" {
			symbol = "EURUSD"
		}
		mappedSymbol := applyCurrencyMaps(symbol, c.Currency_maps)

		// Aggregate ticks into bars
		price := float64((tick.Ask + tick.Bid) / 2)
		ts := time.UnixMilli(tick.Time)
		timeframe := c.Scalping.Timeframe
		if timeframe == "" {
			timeframe = "M1"
		}
		bucket := bucketStart(ts, timeframe)

		if curBar == nil || curBar.T != bucket.UnixMilli() {
			if curBar != nil {
				// Finalize previous bar and update indicators
				bars = append(bars, *curBar)
				if len(bars) > 1000 {
					bars = bars[len(bars)-500:]
				}

				fastEMA.Add(curBar.C)
				slowEMA.Add(curBar.C)
				rsi.Add(curBar.C)
				bb.Add(curBar.C)

				// Update advanced indicators if enabled
				if macd != nil {
					macd.Add(curBar.C)
				}
				if stochastic != nil {
					stochastic.Add(curBar.H, curBar.L, curBar.C)
				}
				if williamsR != nil {
					williamsR.Add(curBar.H, curBar.L, curBar.C)
				}
				if parabolicSAR != nil {
					parabolicSAR.Add(curBar.H, curBar.L, curBar.C)
				}
				if atr != nil {
					atr.Add(curBar.H, curBar.L, curBar.C)
				}

				tl.Log("[gringo] closed bar:", curBar.T, "OHLC:", curBar.O, curBar.H, curBar.L, curBar.C)
			}
			// Start new bar
			curBar = &Bar{O: price, H: price, L: price, C: price, T: bucket.UnixMilli()}
		} else {
			// Update current bar
			if price > curBar.H {
				curBar.H = price
			}
			if price < curBar.L {
				curBar.L = price
			}
			curBar.C = price
		}

		// Check if indicators are ready
		if !initialized || fastEMA == nil || slowEMA == nil || rsi == nil || bb == nil {
			tl.Log("[gringo] indicators not ready yet")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// Daily reset
		today := time.Now().Format("2006-01-02")
		if today != dayStart {
			dayStart = today
			dailyTrades = 0
			tl.Log("[gringo] new day, reset counters")
		}

		// Risk checks
		if c.Risk.Max_daily_trades > 0 && dailyTrades >= c.Risk.Max_daily_trades {
			tl.Log("[gringo] max daily trades reached:", dailyTrades)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// Check spread
		spread := float64(tick.Ask - tick.Bid)
		maxSpread := c.Scalping.Max_spread_pips
		if maxSpread == 0 {
			maxSpread = 0.0002 // 2 pips for EURUSD
		}
		if spread > maxSpread {
			tl.Log("[gringo] spread too wide:", spread, "max:", maxSpread)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// Get current positions
		positions, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
		if err != nil {
			tl.Log("[gringo] failed to get positions:", err)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		hasPosition := false
		for _, p := range positions.Positions {
			if strings.EqualFold(p.Symbol, mappedSymbol) {
				hasPosition = true
				break
			}
		}

		// Only trade on new bars to avoid over-trading
		if len(bars) == 0 {
			tl.Log("[gringo] no bars available yet")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		lastClosedTs := bars[len(bars)-1].T
		if actedBarTs == lastClosedTs {
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// SOPHISTICATED MULTI-INDICATOR STRATEGIES

		// Get basic indicator values
		fastVal := fastEMA.Value()
		slowVal := slowEMA.Value()
		rsiVal := rsi.Value()
		bbUpper := bb.Upper()
		bbLower := bb.Lower()
		bbMiddle := bb.Middle()

		// Calculate signal strengths for different strategies
		var trendSignal, momentumSignal, meanReversionSignal, multiIndicatorSignal float64

		// 1. TREND FOLLOWING STRATEGY
		emaBullish := fastVal > slowVal
		emaBearish := fastVal < slowVal
		trendStrength := math.Abs(fastVal-slowVal) / slowVal * 100

		if emaBullish {
			trendSignal = math.Min(trendStrength/2, 1.0) // Cap at 1.0
		} else if emaBearish {
			trendSignal = -math.Min(trendStrength/2, 1.0)
		}

		// 2. MOMENTUM STRATEGY
		recentBars := 3
		if len(bars) >= recentBars+1 {
			momentum := bars[len(bars)-1].C - bars[len(bars)-recentBars].C
			minMove := c.Scalping.Min_pip_move
			if minMove == 0 {
				minMove = 0.0005
			}

			momentumStrength := math.Abs(momentum) / minMove
			if momentum > minMove {
				momentumSignal = math.Min(momentumStrength/5, 1.0)
			} else if momentum < -minMove {
				momentumSignal = -math.Min(momentumStrength/5, 1.0)
			}
		}

		// 3. MEAN REVERSION STRATEGY
		bbRange := bbUpper - bbLower
		if bbRange > 0 {
			bbPosition := (price - bbMiddle) / (bbRange / 2)
			rsiPosition := (rsiVal - 50) / 50

			// Mean reversion signals are opposite to current position
			if bbPosition > 0.8 && rsiVal > 70 {
				meanReversionSignal = -math.Min(bbPosition, 1.0)
			} else if bbPosition < -0.8 && rsiVal < 30 {
				meanReversionSignal = math.Min(-bbPosition, 1.0)
			}
		}

		// 4. MULTI-INDICATOR CONFIRMATION STRATEGY
		var advancedSignals []float64

		// MACD signals
		if macd != nil && macd.IsInit() {
			macdLine := macd.MACD()
			macdSignal := macd.Signal()
			macdHist := macd.Histogram()

			if macdLine > macdSignal && macdHist > 0 {
				advancedSignals = append(advancedSignals, 0.8)
			} else if macdLine < macdSignal && macdHist < 0 {
				advancedSignals = append(advancedSignals, -0.8)
			}
		}

		// Stochastic signals
		if stochastic != nil && stochastic.IsInit() {
			stochK := stochastic.K()
			if stochK < c.Advanced_indicators.Stoch_oversold {
				advancedSignals = append(advancedSignals, 0.6)
			} else if stochK > c.Advanced_indicators.Stoch_overbought {
				advancedSignals = append(advancedSignals, -0.6)
			}
		}

		// Williams %R signals
		if williamsR != nil && williamsR.IsInit() {
			wrVal := williamsR.Value()
			if wrVal < c.Advanced_indicators.Williams_oversold {
				advancedSignals = append(advancedSignals, 0.5)
			} else if wrVal > c.Advanced_indicators.Williams_overbought {
				advancedSignals = append(advancedSignals, -0.5)
			}
		}

		// Parabolic SAR signals
		if parabolicSAR != nil && parabolicSAR.IsInit() {
			sarVal := parabolicSAR.Value()
			if price > sarVal && parabolicSAR.IsLong() {
				advancedSignals = append(advancedSignals, 0.7)
			} else if price < sarVal && !parabolicSAR.IsLong() {
				advancedSignals = append(advancedSignals, -0.7)
			}
		}

		// Average advanced signals
		if len(advancedSignals) > 0 {
			sum := 0.0
			for _, sig := range advancedSignals {
				sum += sig
			}
			multiIndicatorSignal = sum / float64(len(advancedSignals))
		}

		// COMBINE ALL STRATEGIES WITH WEIGHTS
		weights := c.Strategies
		if weights.Trend_following_weight == 0 && weights.Momentum_weight == 0 &&
			weights.Mean_reversion_weight == 0 && weights.Multi_indicator_weight == 0 {
			// Default weights if not configured
			weights.Trend_following_weight = 0.3
			weights.Momentum_weight = 0.25
			weights.Mean_reversion_weight = 0.2
			weights.Multi_indicator_weight = 0.25
		}

		combinedSignal := (trendSignal * weights.Trend_following_weight) +
			(momentumSignal * weights.Momentum_weight) +
			(meanReversionSignal * weights.Mean_reversion_weight) +
			(multiIndicatorSignal * weights.Multi_indicator_weight)

		minSignalStrength := weights.Min_signal_strength
		if minSignalStrength == 0 {
			minSignalStrength = 0.4 // Default minimum signal strength
		}

		tl.Log("[gringo] Signals - Trend:", trendSignal, "Momentum:", momentumSignal, "MeanRev:", meanReversionSignal, "MultiInd:", multiIndicatorSignal, "Combined:", combinedSignal)

		// ENTRY SIGNALS BASED ON COMBINED ANALYSIS
		var signal string
		var actionType int64

		if !hasPosition && combinedSignal > minSignalStrength {
			signal = "LONG"
			actionType = 0
		} else if !hasPosition && combinedSignal < -minSignalStrength {
			signal = "SHORT"
			actionType = 1
		}

		if signal != "" {
			tl.Log("[gringo] SIGNAL:", signal, "EMA:", fastVal, "/", slowVal, "RSI:", rsiVal, "BB:", bbLower, "/", bbUpper, "Price:", price)

			if c.Dry_run {
				tl.Log("[gringo] DRY RUN - would place", signal, "trade")
			} else {
				// Calculate position size and levels
				vol := calculateVolume(o, c, mappedSymbol)
				if vol > 0 {
					stopPips := c.Scalping.Stop_loss_pips
					if stopPips == 0 {
						stopPips = 10 // 10 pips default
					}
					profitPips := c.Scalping.Quick_profit_pips
					if profitPips == 0 {
						profitPips = 5 // 5 pips default
					}

					// Determine trading mode and calculate pip value
					tradingMode := determineTradingMode(mappedSymbol, c.Trading_mode)
					pipValue := calculatePipValue(mappedSymbol, tradingMode, c)

					var sl, tp *float32
					if actionType == 0 { // Long
						slPrice := float32(price - stopPips*pipValue)
						tpPrice := float32(price + profitPips*pipValue)
						sl = &slPrice
						tp = &tpPrice
						tl.Log("[gringo] LONG levels: entry=", price, "SL=", slPrice, "TP=", tpPrice)
					} else { // Short
						slPrice := float32(price + stopPips*pipValue)
						tpPrice := float32(price - profitPips*pipValue)
						sl = &slPrice
						tp = &tpPrice
						tl.Log("[gringo] SHORT levels: entry=", price, "SL=", slPrice, "TP=", tpPrice)
					}

					comment := "Trade by Gringo"

					magic := computeMagic(o.Id)
					_, err := o.Client.PlaceTrade(o.Ctx, &PlaceTradeRequest{
						Symbol:     mappedSymbol,
						ActionType: actionType,
						Volume:     vol,
						StopLoss:   sl,
						TakeProfit: tp,
						Magic:      &magic,
						Comment:    &comment,
					})

					if err != nil {
						tl.Log("[gringo] trade error:", err.Error())
					} else {
						tl.Log("[gringo] placed", signal, "trade vol:", vol, "SL:", *sl, "TP:", *tp)
						lastTradeAt = time.Now()
						actedBarTs = lastClosedTs
						dailyTrades++
					}
				}
			}
		}

		out := append([]string(nil), tl.lines...)
		tl.lines = tl.lines[:0]
		return out
	}

	Initialize(&config, fn)
}

func calculateVolume(_ Options, c Config, _ string) float32 {
	if c.Scalping.Fixed_volume > 0 {
		return c.Scalping.Fixed_volume
	}
	// Use larger volume for more visible P&L in backtests
	return 0.1 // 0.01 lots = 1000 units for forex
}
