#!/usr/bin/env python3
import json
import subprocess
import re
import time
from datetime import datetime, timedelta
import itertools

# ETHUSD optimization parameters
TIMEFRAMES = ["M1", "M5", "M15"]
MONTHS_BACK = [1, 2, 3, 6]  # Different month intervals

# Ethereum scalping parameter sets - optimized for ETH volatility
ETH_SCALPING_PARAMS = [
    # Ultra-fast ETH scalping (M1)
    {
        "fast_ema_period": 5, "slow_ema_period": 13,
        "rsi_period": 9, "rsi_oversold": 20, "rsi_overbought": 80,
        "bb_period": 12, "bb_deviation": 1.8,
        "min_pip_move": 1.0, "quick_profit_pips": 8, "stop_loss_pips": 15,
        "risk_per_trade_pct": 0.8, "fixed_volume": 0.01,
        "min_trade_interval_ms": 200
    },
    # Fast ETH scalping
    {
        "fast_ema_period": 8, "slow_ema_period": 21,
        "rsi_period": 14, "rsi_oversold": 25, "rsi_overbought": 75,
        "bb_period": 20, "bb_deviation": 2.0,
        "min_pip_move": 2.0, "quick_profit_pips": 12, "stop_loss_pips": 25,
        "risk_per_trade_pct": 1.0, "fixed_volume": 0.015,
        "min_trade_interval_ms": 400
    },
    # Medium ETH scalping
    {
        "fast_ema_period": 12, "slow_ema_period": 26,
        "rsi_period": 14, "rsi_oversold": 30, "rsi_overbought": 70,
        "bb_period": 20, "bb_deviation": 2.2,
        "min_pip_move": 3.0, "quick_profit_pips": 18, "stop_loss_pips": 35,
        "risk_per_trade_pct": 1.2, "fixed_volume": 0.02,
        "min_trade_interval_ms": 600
    },
    # Conservative ETH
    {
        "fast_ema_period": 21, "slow_ema_period": 55,
        "rsi_period": 21, "rsi_oversold": 35, "rsi_overbought": 65,
        "bb_period": 25, "bb_deviation": 2.5,
        "min_pip_move": 5.0, "quick_profit_pips": 25, "stop_loss_pips": 50,
        "risk_per_trade_pct": 1.5, "fixed_volume": 0.025,
        "min_trade_interval_ms": 1000
    },
    # Trend following ETH
    {
        "fast_ema_period": 8, "slow_ema_period": 34,
        "rsi_period": 14, "rsi_oversold": 15, "rsi_overbought": 85,
        "bb_period": 18, "bb_deviation": 1.9,
        "min_pip_move": 4.0, "quick_profit_pips": 20, "stop_loss_pips": 40,
        "risk_per_trade_pct": 1.1, "fixed_volume": 0.018,
        "min_trade_interval_ms": 500
    },
    # Breakout ETH scalping
    {
        "fast_ema_period": 5, "slow_ema_period": 20,
        "rsi_period": 10, "rsi_oversold": 10, "rsi_overbought": 90,
        "bb_period": 15, "bb_deviation": 2.8,
        "min_pip_move": 6.0, "quick_profit_pips": 30, "stop_loss_pips": 60,
        "risk_per_trade_pct": 1.3, "fixed_volume": 0.022,
        "min_trade_interval_ms": 300
    },
    # High-frequency ETH
    {
        "fast_ema_period": 3, "slow_ema_period": 8,
        "rsi_period": 7, "rsi_oversold": 25, "rsi_overbought": 75,
        "bb_period": 10, "bb_deviation": 1.5,
        "min_pip_move": 0.8, "quick_profit_pips": 6, "stop_loss_pips": 12,
        "risk_per_trade_pct": 0.6, "fixed_volume": 0.008,
        "min_trade_interval_ms": 150
    },
    # Volatility expansion ETH
    {
        "fast_ema_period": 10, "slow_ema_period": 30,
        "rsi_period": 16, "rsi_oversold": 20, "rsi_overbought": 80,
        "bb_period": 22, "bb_deviation": 2.6,
        "min_pip_move": 7.0, "quick_profit_pips": 35, "stop_loss_pips": 70,
        "risk_per_trade_pct": 1.4, "fixed_volume": 0.028,
        "min_trade_interval_ms": 800
    }
]

def get_timestamp_months_back(months):
    """Get timestamp for N months back"""
    now = datetime.now()
    past = now - timedelta(days=months * 30)
    return int(past.timestamp() * 1000)

def update_env_config(timeframe, months_back, params):
    """Update env.ethusd.json with test parameters"""
    with open('env.ethusd.json', 'r') as f:
        config = json.load(f)
    
    # Update basic settings
    config['scalping']['timeframe'] = timeframe
    config['backtest_timeframe'] = timeframe
    config['backtest_from'] = get_timestamp_months_back(months_back)
    config['min_trade_interval_ms'] = params.get('min_trade_interval_ms', 500)
    
    # Adjust bar count and spread based on timeframe
    if timeframe == "M1":
        config['backtest_count'] = 4000
        config['simulated_spread'] = 0.3
        config['risk']['max_concurrent_trades'] = 2
        config['risk']['max_daily_trades'] = 400
    elif timeframe == "M5":
        config['backtest_count'] = 3000
        config['simulated_spread'] = 0.4
        config['risk']['max_concurrent_trades'] = 3
        config['risk']['max_daily_trades'] = 200
    else:  # M15
        config['backtest_count'] = 2000
        config['simulated_spread'] = 0.6
        config['risk']['max_concurrent_trades'] = 5
        config['risk']['max_daily_trades'] = 100
    
    # Update scalping parameters
    for key, value in params.items():
        if key in config['scalping'] and key != 'min_trade_interval_ms':
            config['scalping'][key] = value
    
    with open('env.ethusd.json', 'w') as f:
        json.dump(config, f, indent=2)

def run_backtest():
    """Run a single backtest and extract results"""
    try:
        result = subprocess.run([
            'go', 'run', '.', 
            '-auth_token', 'test_terminal_token',
            '-id', 'gringo-ethusd-opt',
            '-rpc_uri', '0.0.0.0:50050',
            '-ws_uri', 'ws://localhost:4010/bots',
            '-env_file', './env.ethusd.json'
        ], capture_output=True, text=True, timeout=120)
        
        output = result.stdout + result.stderr
        
        # Extract metrics from enhanced report
        pnl_match = re.search(r'Total P&L: \$([-+]?\d+\.?\d*)', output)
        equity_match = re.search(r'End equity: \$([\d.]+)', output)
        drawdown_match = re.search(r'Max drawdown: \$([\d.]+)', output)
        trades_match = re.search(r'Trades closed: (\d+)', output)
        placed_match = re.search(r'Total trades placed: (\d+)', output)
        win_rate_match = re.search(r'Win rate: ([\d.]+)%', output)
        blown_match = re.search(r'Account status: (BLOWN OUT|ACTIVE)', output)
        max_wins_match = re.search(r'Max consecutive wins: (\d+)', output)
        max_losses_match = re.search(r'Max consecutive losses: (\d+)', output)
        
        pnl = float(pnl_match.group(1)) if pnl_match else 0.0
        equity = float(equity_match.group(1)) if equity_match else 10000.0
        drawdown = float(drawdown_match.group(1)) if drawdown_match else 0.0
        trades_closed = int(trades_match.group(1)) if trades_match else 0
        trades_placed = int(placed_match.group(1)) if placed_match else 0
        win_rate = float(win_rate_match.group(1)) if win_rate_match else 0.0
        is_blown = blown_match.group(1) == "BLOWN OUT" if blown_match else False
        max_wins = int(max_wins_match.group(1)) if max_wins_match else 0
        max_losses = int(max_losses_match.group(1)) if max_losses_match else 0
        
        # Calculate ETH-specific score (balance profit, consistency, and risk)
        score = 0.0
        if not is_blown and trades_closed > 0:
            # ETH scoring: P&L (35%), Win Rate (25%), Trade Activity (15%), Low Drawdown (15%), Streak Quality (10%)
            drawdown_penalty = min(drawdown / 2000.0, 1.0) * 150  # Penalize high drawdowns
            streak_quality = (max_wins * 3) - (max_losses * 2)  # Reward win streaks, penalize loss streaks
            activity_bonus = min(trades_closed / 100.0, 2.0) * 20  # Reward active trading
            score = (pnl * 0.35) + (win_rate * 0.25) + (activity_bonus * 0.15) - (drawdown_penalty * 0.15) + (streak_quality * 0.1)
        
        return {
            'pnl': pnl, 'equity': equity, 'drawdown': drawdown,
            'trades_closed': trades_closed, 'trades_placed': trades_placed,
            'win_rate': win_rate, 'is_blown': is_blown,
            'max_wins': max_wins, 'max_losses': max_losses,
            'score': score, 'success': True,
            'output_snippet': output[-400:]
        }
    except Exception as e:
        return {
            'pnl': -3000.0, 'equity': 0.0, 'drawdown': 3000.0,
            'trades_closed': 0, 'trades_placed': 0, 'win_rate': 0.0,
            'is_blown': True, 'max_wins': 0, 'max_losses': 50,
            'score': -500.0, 'success': False, 'error': str(e)
        }

def main():
    results = []
    test_count = 0
    total_tests = len(TIMEFRAMES) * len(MONTHS_BACK) * len(ETH_SCALPING_PARAMS)
    
    print(f"⟠ GRINGO BOT ETHUSD ETHEREUM OPTIMIZATION")
    print(f"Testing {total_tests} combinations across timeframes and months")
    print("="*80)
    
    for timeframe in TIMEFRAMES:
        for months_back in MONTHS_BACK:
            for i, params in enumerate(ETH_SCALPING_PARAMS):
                test_count += 1
                param_names = ["Ultra-Fast", "Fast", "Medium", "Conservative", "Trend", "Breakout", "High-Freq", "Volatility"]
                param_name = param_names[i]
                
                print(f"\n[{test_count}/{total_tests}] {timeframe} {months_back}M {param_name}")
                print(f"EMA: {params['fast_ema_period']}/{params['slow_ema_period']} | "
                      f"RSI: {params['rsi_period']}({params['rsi_oversold']}/{params['rsi_overbought']}) | "
                      f"TP/SL: {params['quick_profit_pips']}/{params['stop_loss_pips']} | "
                      f"Vol: {params['fixed_volume']}")
                
                # Update configuration
                update_env_config(timeframe, months_back, params)
                
                # Run backtest
                result = run_backtest()
                
                # Store results
                test_result = {
                    'timeframe': timeframe, 'months_back': months_back,
                    'param_set': i + 1, 'param_name': param_name,
                    'params': params, **result
                }
                results.append(test_result)
                
                # Display results
                status = "BLOWN" if result['is_blown'] else "ACTIVE"
                print(f"Result: P&L=${result['pnl']:.2f} | Trades={result['trades_closed']}/{result['trades_placed']} | "
                      f"Win%={result['win_rate']:.1f} | DD=${result['drawdown']:.2f} | "
                      f"Streaks={result['max_wins']}/{result['max_losses']} | Score={result['score']:.1f} | {status}")
                
                time.sleep(0.6)
    
    # Sort by score (best overall performance)
    results.sort(key=lambda x: x['score'], reverse=True)
    
    # Save detailed results
    with open('ethusd_optimization_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Filter winning configurations
    winning_results = [r for r in results if r['success'] and not r['is_blown'] and r['trades_closed'] > 0 and r['pnl'] > 0]
    
    print("\n" + "="*80)
    print("⟠ TOP WINNING GRINGO BOT CONFIGURATIONS FOR ETHUSD:")
    print("="*80)
    
    if winning_results:
        for i, result in enumerate(winning_results[:10]):
            print(f"\n#{i+1} - Score: {result['score']:.1f} | P&L: ${result['pnl']:.2f}")
            print(f"Timeframe: {result['timeframe']} | Period: {result['months_back']} months | Style: {result['param_name']}")
            print(f"EMA: {result['params']['fast_ema_period']}/{result['params']['slow_ema_period']} | "
                  f"RSI: {result['params']['rsi_period']}({result['params']['rsi_oversold']}/{result['params']['rsi_overbought']})")
            print(f"TP/SL: {result['params']['quick_profit_pips']}/{result['params']['stop_loss_pips']} pips | "
                  f"Volume: {result['params']['fixed_volume']} | Interval: {result['params']['min_trade_interval_ms']}ms")
            print(f"Performance: {result['trades_closed']} trades, {result['win_rate']:.1f}% win rate, "
                  f"${result['drawdown']:.2f} max DD, {result['max_wins']}/{result['max_losses']} streaks")
        
        # Update config with best winning configuration
        best = winning_results[0]
        print(f"\n" + "="*80)
        print("⟠ UPDATING CONFIG WITH BEST WINNING ETHUSD CONFIGURATION:")
        print("="*80)
        update_env_config(best['timeframe'], best['months_back'], best['params'])
        print(f"Updated env.ethusd.json with: {best['timeframe']} {best['param_name']} "
              f"(Score: {best['score']:.1f}, P&L: ${best['pnl']:.2f})")
    else:
        print("❌ NO WINNING CONFIGURATIONS FOUND!")
        print("Ethereum volatility may require different approaches:")
        print("- Consider wider spreads for ETH volatility")
        print("- Test different time periods (ETH has different cycles)")
        print("- Adjust position sizing for crypto market conditions")
        print("- Consider DeFi activity impact on ETH price movements")

if __name__ == "__main__":
    main()
